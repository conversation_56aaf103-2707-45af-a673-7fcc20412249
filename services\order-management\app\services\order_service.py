"""Order service for business logic."""

from datetime import datetime
from typing import List, Optional, Tuple, Dict, Any
from uuid import UUID

from sqlalchemy import select, func, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
import structlog

from app.models.order import OrderModel, OrderItemModel
from shared.models.order import (
    Order, OrderCreate, OrderUpdate, OrderStatus, 
    OrderType, OrderPriority, OrderSummary
)

logger = structlog.get_logger(__name__)


class OrderService:
    """Service class for order management operations."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_order(self, order_data: OrderCreate) -> Order:
        """Create a new order."""
        try:
            # Generate order number
            order_number = await self._generate_order_number()
            
            # Create order model
            order_model = OrderModel(
                order_number=order_number,
                customer_id=order_data.customer_id,
                order_type=order_data.order_type,
                priority=order_data.priority,
                requested_completion_date=order_data.requested_completion_date,
                contact_person=order_data.contact_person,
                contact_phone=order_data.contact_phone,
                contact_email=order_data.contact_email,
                service_address=order_data.service_address,
                notes=order_data.notes,
                external_reference=order_data.external_reference,
                tags=order_data.tags,
            )
            
            self.db.add(order_model)
            await self.db.commit()
            await self.db.refresh(order_model)
            
            logger.info("Order created", order_id=str(order_model.id))
            
            return await self._model_to_pydantic(order_model)
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to create order", exc_info=e)
            raise
    
    async def get_order(self, order_id: UUID) -> Optional[Order]:
        """Get an order by ID."""
        try:
            stmt = (
                select(OrderModel)
                .options(selectinload(OrderModel.items))
                .where(OrderModel.id == order_id)
            )
            result = await self.db.execute(stmt)
            order_model = result.scalar_one_or_none()
            
            if not order_model:
                return None
            
            return await self._model_to_pydantic(order_model)
            
        except Exception as e:
            logger.error("Failed to get order", order_id=str(order_id), exc_info=e)
            raise
    
    async def list_orders(
        self, 
        offset: int = 0, 
        limit: int = 20,
        filters: Optional[Dict[str, Any]] = None
    ) -> Tuple[List[OrderSummary], int]:
        """List orders with pagination and filtering."""
        try:
            # Build base query
            stmt = select(OrderModel)
            count_stmt = select(func.count(OrderModel.id))
            
            # Apply filters
            if filters:
                conditions = []
                for key, value in filters.items():
                    if hasattr(OrderModel, key):
                        conditions.append(getattr(OrderModel, key) == value)
                
                if conditions:
                    filter_condition = and_(*conditions)
                    stmt = stmt.where(filter_condition)
                    count_stmt = count_stmt.where(filter_condition)
            
            # Get total count
            count_result = await self.db.execute(count_stmt)
            total = count_result.scalar()
            
            # Get paginated results
            stmt = stmt.offset(offset).limit(limit).order_by(OrderModel.created_at.desc())
            result = await self.db.execute(stmt)
            order_models = result.scalars().all()
            
            # Convert to summary objects
            orders = []
            for order_model in order_models:
                # Count items
                items_count_stmt = select(func.count(OrderItemModel.id)).where(
                    OrderItemModel.order_id == order_model.id
                )
                items_result = await self.db.execute(items_count_stmt)
                items_count = items_result.scalar() or 0
                
                order_summary = OrderSummary(
                    id=order_model.id,
                    order_number=order_model.order_number,
                    customer_id=order_model.customer_id,
                    order_type=order_model.order_type,
                    status=order_model.status,
                    priority=order_model.priority,
                    order_date=order_model.order_date,
                    total_amount=order_model.total_amount,
                    items_count=items_count,
                    requested_completion_date=order_model.requested_completion_date,
                    scheduled_completion_date=order_model.scheduled_completion_date,
                )
                orders.append(order_summary)
            
            return orders, total
            
        except Exception as e:
            logger.error("Failed to list orders", exc_info=e)
            raise
    
    async def update_order(self, order_id: UUID, order_data: OrderUpdate) -> Optional[Order]:
        """Update an existing order."""
        try:
            stmt = select(OrderModel).where(OrderModel.id == order_id)
            result = await self.db.execute(stmt)
            order_model = result.scalar_one_or_none()
            
            if not order_model:
                return None
            
            # Check if order can be updated
            if order_model.status in [OrderStatus.COMPLETED, OrderStatus.CANCELLED]:
                raise ValueError("Cannot update completed or cancelled orders")
            
            # Update fields
            update_data = order_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                if hasattr(order_model, field):
                    setattr(order_model, field, value)
            
            order_model.updated_at = datetime.utcnow()
            
            await self.db.commit()
            await self.db.refresh(order_model)
            
            logger.info("Order updated", order_id=str(order_id))
            
            return await self._model_to_pydantic(order_model)
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to update order", order_id=str(order_id), exc_info=e)
            raise
    
    async def cancel_order(self, order_id: UUID) -> bool:
        """Cancel an order."""
        try:
            stmt = select(OrderModel).where(OrderModel.id == order_id)
            result = await self.db.execute(stmt)
            order_model = result.scalar_one_or_none()
            
            if not order_model:
                return False
            
            # Check if order can be cancelled
            if not order_model.status in [OrderStatus.DRAFT, OrderStatus.SUBMITTED, OrderStatus.APPROVED]:
                raise ValueError("Order cannot be cancelled in current status")
            
            order_model.status = OrderStatus.CANCELLED
            order_model.updated_at = datetime.utcnow()
            
            await self.db.commit()
            
            logger.info("Order cancelled", order_id=str(order_id))
            
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to cancel order", order_id=str(order_id), exc_info=e)
            raise
    
    async def submit_order(self, order_id: UUID) -> Optional[Order]:
        """Submit an order for processing."""
        try:
            stmt = (
                select(OrderModel)
                .options(selectinload(OrderModel.items))
                .where(OrderModel.id == order_id)
            )
            result = await self.db.execute(stmt)
            order_model = result.scalar_one_or_none()
            
            if not order_model:
                return None
            
            # Validate order can be submitted
            if order_model.status != OrderStatus.DRAFT:
                raise ValueError("Only draft orders can be submitted")
            
            if not order_model.items:
                raise ValueError("Order must have at least one item")
            
            # Update status
            order_model.status = OrderStatus.SUBMITTED
            order_model.updated_at = datetime.utcnow()
            
            # TODO: Trigger workflow/orchestration
            # await self._trigger_order_workflow(order_model)
            
            await self.db.commit()
            await self.db.refresh(order_model)
            
            logger.info("Order submitted", order_id=str(order_id))
            
            return await self._model_to_pydantic(order_model)
            
        except Exception as e:
            await self.db.rollback()
            logger.error("Failed to submit order", order_id=str(order_id), exc_info=e)
            raise
    
    async def _generate_order_number(self) -> str:
        """Generate a unique order number."""
        # Simple implementation - in production, you might want a more sophisticated approach
        timestamp = datetime.utcnow().strftime("%Y%m%d%H%M%S")
        
        # Get count of orders today
        today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        stmt = select(func.count(OrderModel.id)).where(OrderModel.created_at >= today_start)
        result = await self.db.execute(stmt)
        count = result.scalar() or 0
        
        return f"ORD-{timestamp}-{count + 1:04d}"
    
    async def _model_to_pydantic(self, order_model: OrderModel) -> Order:
        """Convert SQLAlchemy model to Pydantic model."""
        # Convert order items
        items = []
        for item_model in order_model.items:
            # Convert to Pydantic - simplified for now
            items.append({
                "id": item_model.id,
                "order_id": item_model.order_id,
                "service_id": item_model.service_id,
                "service_plan_id": item_model.service_plan_id,
                "item_type": item_model.item_type,
                "description": item_model.description,
                "quantity": item_model.quantity,
                "unit_price": item_model.unit_price,
                "total_price": item_model.total_price,
                "configuration": item_model.configuration,
                "status": item_model.status,
                "provisioning_status": item_model.provisioning_status,
                "created_at": item_model.created_at,
                "updated_at": item_model.updated_at,
                "requested_date": item_model.requested_date,
                "scheduled_date": item_model.scheduled_date,
                "completed_date": item_model.completed_date,
                "notes": item_model.notes,
                "external_reference": item_model.external_reference,
            })
        
        return Order(
            id=order_model.id,
            order_number=order_model.order_number,
            customer_id=order_model.customer_id,
            order_type=order_model.order_type,
            status=order_model.status,
            priority=order_model.priority,
            created_at=order_model.created_at,
            updated_at=order_model.updated_at,
            order_date=order_model.order_date,
            requested_completion_date=order_model.requested_completion_date,
            scheduled_completion_date=order_model.scheduled_completion_date,
            actual_completion_date=order_model.actual_completion_date,
            subtotal=order_model.subtotal,
            tax_amount=order_model.tax_amount,
            total_amount=order_model.total_amount,
            items=items,
            contact_person=order_model.contact_person,
            contact_phone=order_model.contact_phone,
            contact_email=order_model.contact_email,
            service_address=order_model.service_address,
            workflow_id=order_model.workflow_id,
            assigned_technician=order_model.assigned_technician,
            notes=order_model.notes,
            internal_notes=order_model.internal_notes,
            external_reference=order_model.external_reference,
            tags=order_model.tags or [],
        )
