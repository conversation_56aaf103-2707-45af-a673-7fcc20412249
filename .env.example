# TeleBilling Environment Configuration

# Application
DEBUG=true
LOG_LEVEL=INFO
LOG_FORMAT=json

# Database Configuration
DATABASE_URL=postgresql://telebilling:telebilling_dev_password@localhost:5432/order_management
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_POOL_SIZE=10

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_GROUP_ID=order-management

# Security
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# CORS
ALLOWED_HOSTS=*

# External Services
INVENTORY_SERVICE_URL=http://inventory-management:8000
BILLING_SERVICE_URL=http://billing-integration:8000
ORCHESTRATION_SERVICE_URL=http://orchestration-engine:8000

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Monitoring
ENABLE_METRICS=true
ENABLE_TRACING=true
JAEGER_ENDPOINT=http://jaeger:14268

# Business Rules
MAX_ORDER_ITEMS=50
ORDER_TIMEOUT_HOURS=24
AUTO_APPROVE_THRESHOLD=1000.0
