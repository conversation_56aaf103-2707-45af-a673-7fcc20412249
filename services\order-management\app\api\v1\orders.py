"""Order management API endpoints."""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from app.core.database import get_db
from app.services.order_service import OrderService
from shared.models.base import APIResponse, PaginationParams, PaginatedResponse
from shared.models.order import (
    Order, OrderCreate, OrderUpdate, OrderSummary, 
    OrderStatus, OrderType, OrderPriority
)

router = APIRouter()
logger = structlog.get_logger(__name__)


@router.post("/", response_model=APIResponse, status_code=status.HTTP_201_CREATED)
async def create_order(
    order_data: OrderCreate,
    db: AsyncSession = Depends(get_db)
) -> APIResponse:
    """Create a new order."""
    try:
        order_service = OrderService(db)
        order = await order_service.create_order(order_data)
        
        logger.info("Order created", order_id=str(order.id), order_number=order.order_number)
        
        return APIResponse.success_response(
            data={"order": order.dict()},
            message="Order created successfully"
        )
    except ValueError as e:
        logger.warning("Order creation validation error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Order creation failed", exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create order"
        )


@router.get("/", response_model=PaginatedResponse)
async def list_orders(
    pagination: PaginationParams = Depends(),
    status_filter: Optional[OrderStatus] = Query(None, alias="status"),
    type_filter: Optional[OrderType] = Query(None, alias="type"),
    priority_filter: Optional[OrderPriority] = Query(None, alias="priority"),
    customer_id: Optional[UUID] = Query(None),
    db: AsyncSession = Depends(get_db)
) -> PaginatedResponse:
    """List orders with filtering and pagination."""
    try:
        order_service = OrderService(db)
        
        filters = {}
        if status_filter:
            filters["status"] = status_filter
        if type_filter:
            filters["order_type"] = type_filter
        if priority_filter:
            filters["priority"] = priority_filter
        if customer_id:
            filters["customer_id"] = customer_id
        
        orders, total = await order_service.list_orders(
            offset=pagination.offset,
            limit=pagination.size,
            filters=filters
        )
        
        return PaginatedResponse.create(
            items=[order.dict() for order in orders],
            total=total,
            page=pagination.page,
            size=pagination.size
        )
    except Exception as e:
        logger.error("Failed to list orders", exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve orders"
        )


@router.get("/{order_id}", response_model=APIResponse)
async def get_order(
    order_id: UUID,
    db: AsyncSession = Depends(get_db)
) -> APIResponse:
    """Get a specific order by ID."""
    try:
        order_service = OrderService(db)
        order = await order_service.get_order(order_id)
        
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order not found"
            )
        
        return APIResponse.success_response(
            data={"order": order.dict()},
            message="Order retrieved successfully"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get order", order_id=str(order_id), exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve order"
        )


@router.put("/{order_id}", response_model=APIResponse)
async def update_order(
    order_id: UUID,
    order_data: OrderUpdate,
    db: AsyncSession = Depends(get_db)
) -> APIResponse:
    """Update an existing order."""
    try:
        order_service = OrderService(db)
        order = await order_service.update_order(order_id, order_data)
        
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order not found"
            )
        
        logger.info("Order updated", order_id=str(order_id))
        
        return APIResponse.success_response(
            data={"order": order.dict()},
            message="Order updated successfully"
        )
    except HTTPException:
        raise
    except ValueError as e:
        logger.warning("Order update validation error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Order update failed", order_id=str(order_id), exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update order"
        )


@router.delete("/{order_id}", response_model=APIResponse)
async def cancel_order(
    order_id: UUID,
    db: AsyncSession = Depends(get_db)
) -> APIResponse:
    """Cancel an order (soft delete)."""
    try:
        order_service = OrderService(db)
        success = await order_service.cancel_order(order_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order not found or cannot be cancelled"
            )
        
        logger.info("Order cancelled", order_id=str(order_id))
        
        return APIResponse.success_response(
            message="Order cancelled successfully"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Order cancellation failed", order_id=str(order_id), exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel order"
        )


@router.post("/{order_id}/submit", response_model=APIResponse)
async def submit_order(
    order_id: UUID,
    db: AsyncSession = Depends(get_db)
) -> APIResponse:
    """Submit an order for processing."""
    try:
        order_service = OrderService(db)
        order = await order_service.submit_order(order_id)
        
        if not order:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Order not found or cannot be submitted"
            )
        
        logger.info("Order submitted", order_id=str(order_id))
        
        return APIResponse.success_response(
            data={"order": order.dict()},
            message="Order submitted successfully"
        )
    except HTTPException:
        raise
    except ValueError as e:
        logger.warning("Order submission validation error", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Order submission failed", order_id=str(order_id), exc_info=e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to submit order"
        )
