version: '3.8'

services:
  # Database Services
  postgres:
    image: postgres:15-alpine
    container_name: telebilling-postgres
    environment:
      POSTGRES_DB: telebilling
      POSTGRES_USER: telebilling
      POSTGRES_PASSWORD: telebilling_dev_password
      POSTGRES_MULTIPLE_DATABASES: order_management,inventory_management,billing_integration
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/development/init-databases.sh:/docker-entrypoint-initdb.d/init-databases.sh
    networks:
      - telebilling-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U telebilling"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: telebilling-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - telebilling-network
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Apache Kafka for event streaming
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: telebilling-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - telebilling-network

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: telebilling-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    networks:
      - telebilling-network
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Monitoring Services
  prometheus:
    image: prom/prometheus:latest
    container_name: telebilling-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - telebilling-network

  grafana:
    image: grafana/grafana:latest
    container_name: telebilling-grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    networks:
      - telebilling-network

  # Elasticsearch for logging
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: telebilling-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - telebilling-network

  # Kibana for log visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: telebilling-kibana
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - telebilling-network

  # Jaeger for distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: telebilling-jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      COLLECTOR_OTLP_ENABLED: true
    networks:
      - telebilling-network

  # Order Management Service
  order-management:
    build:
      context: .
      dockerfile: services/order-management/Dockerfile
    container_name: telebilling-order-management
    ports:
      - "8001:8000"
    environment:
      - DATABASE_URL=***************************************************************/order_management
      - REDIS_URL=redis://redis:6379/0
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - LOG_LEVEL=INFO
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy
    networks:
      - telebilling-network
    volumes:
      - ./services/order-management:/app
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Inventory Management Service
  inventory-management:
    build:
      context: .
      dockerfile: services/inventory-management/Dockerfile
    container_name: telebilling-inventory-management
    ports:
      - "8002:8000"
    environment:
      - DATABASE_URL=***************************************************************/inventory_management
      - REDIS_URL=redis://redis:6379/1
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - LOG_LEVEL=INFO
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy
    networks:
      - telebilling-network
    volumes:
      - ./services/inventory-management:/app
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Billing Integration Service
  billing-integration:
    build:
      context: .
      dockerfile: services/billing-integration/Dockerfile
    container_name: telebilling-billing-integration
    ports:
      - "8003:8000"
    environment:
      - DATABASE_URL=***************************************************************/billing_integration
      - REDIS_URL=redis://redis:6379/2
      - KAFKA_BOOTSTRAP_SERVERS=kafka:29092
      - LOG_LEVEL=INFO
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy
    networks:
      - telebilling-network
    volumes:
      - ./services/billing-integration:/app
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
  elasticsearch_data:

networks:
  telebilling-network:
    driver: bridge
