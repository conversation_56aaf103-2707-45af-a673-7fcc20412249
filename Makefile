# TeleBilling Development Makefile

.PHONY: help install start stop clean test lint format check-format type-check security-check build

# Default target
help:
	@echo "TeleBilling Development Commands"
	@echo "================================"
	@echo ""
	@echo "Setup Commands:"
	@echo "  install          Install all dependencies"
	@echo "  setup            Complete development setup"
	@echo ""
	@echo "Development Commands:"
	@echo "  start            Start all infrastructure services"
	@echo "  stop             Stop all services"
	@echo "  restart          Restart all services"
	@echo "  clean            Clean up containers and volumes"
	@echo ""
	@echo "Service Commands:"
	@echo "  start-order      Start Order Management Service"
	@echo "  start-inventory  Start Inventory Management Service"
	@echo "  start-billing    Start Billing Integration Service"
	@echo ""
	@echo "Testing Commands:"
	@echo "  test             Run all tests"
	@echo "  test-order       Run Order Management tests"
	@echo "  test-coverage    Run tests with coverage report"
	@echo ""
	@echo "Code Quality Commands:"
	@echo "  lint             Run linting checks"
	@echo "  format           Format code with black and isort"
	@echo "  check-format     Check code formatting"
	@echo "  type-check       Run type checking with mypy"
	@echo "  security-check   Run security checks"
	@echo ""
	@echo "Build Commands:"
	@echo "  build            Build all Docker images"
	@echo "  build-order      Build Order Management image"

# Setup Commands
install:
	@echo "📦 Installing dependencies..."
	poetry install
	@echo "✅ Dependencies installed"

setup: install
	@echo "🚀 Setting up development environment..."
	@if [ ! -f .env ]; then cp .env.example .env; echo "📝 Created .env file"; fi
	@chmod +x scripts/development/*.sh
	@echo "✅ Development environment setup complete"

# Development Commands
start:
	@echo "🐳 Starting infrastructure services..."
	docker-compose up -d postgres redis kafka zookeeper prometheus grafana elasticsearch kibana jaeger
	@echo "⏳ Waiting for services to be ready..."
	@sleep 30
	@echo "✅ Infrastructure services started"

stop:
	@echo "🛑 Stopping all services..."
	docker-compose down
	@echo "✅ All services stopped"

restart: stop start

clean:
	@echo "🧹 Cleaning up containers and volumes..."
	docker-compose down -v --remove-orphans
	docker system prune -f
	@echo "✅ Cleanup complete"

# Service Commands
start-order:
	@echo "🚀 Starting Order Management Service..."
	cd services/order-management && poetry run uvicorn app.main:app --reload --port 8001

start-inventory:
	@echo "🚀 Starting Inventory Management Service..."
	cd services/inventory-management && poetry run uvicorn app.main:app --reload --port 8002

start-billing:
	@echo "🚀 Starting Billing Integration Service..."
	cd services/billing-integration && poetry run uvicorn app.main:app --reload --port 8003

# Testing Commands
test:
	@echo "🧪 Running all tests..."
	poetry run pytest -v

test-order:
	@echo "🧪 Running Order Management tests..."
	cd services/order-management && poetry run pytest -v

test-coverage:
	@echo "🧪 Running tests with coverage..."
	poetry run pytest --cov=shared --cov=services --cov-report=html --cov-report=term-missing

# Code Quality Commands
lint:
	@echo "🔍 Running linting checks..."
	poetry run flake8 shared/ services/
	@echo "✅ Linting complete"

format:
	@echo "🎨 Formatting code..."
	poetry run black shared/ services/
	poetry run isort shared/ services/
	@echo "✅ Code formatted"

check-format:
	@echo "🔍 Checking code formatting..."
	poetry run black --check shared/ services/
	poetry run isort --check-only shared/ services/
	@echo "✅ Format check complete"

type-check:
	@echo "🔍 Running type checks..."
	poetry run mypy shared/ services/
	@echo "✅ Type checking complete"

security-check:
	@echo "🔒 Running security checks..."
	poetry run safety check
	@echo "✅ Security check complete"

# Build Commands
build:
	@echo "🏗️  Building all Docker images..."
	docker-compose build
	@echo "✅ All images built"

build-order:
	@echo "🏗️  Building Order Management image..."
	docker-compose build order-management
	@echo "✅ Order Management image built"

# Database Commands
db-migrate:
	@echo "🗄️  Running database migrations..."
	cd services/order-management && poetry run alembic upgrade head
	@echo "✅ Database migrations complete"

db-reset:
	@echo "🗄️  Resetting database..."
	docker-compose down postgres
	docker volume rm telebilling_postgres_data || true
	docker-compose up -d postgres
	@sleep 10
	$(MAKE) db-migrate
	@echo "✅ Database reset complete"

# Monitoring Commands
logs:
	@echo "📋 Showing service logs..."
	docker-compose logs -f

logs-order:
	@echo "📋 Showing Order Management logs..."
	docker-compose logs -f order-management

# Development Utilities
shell-order:
	@echo "🐚 Opening Order Management shell..."
	cd services/order-management && poetry shell

shell-db:
	@echo "🐚 Opening database shell..."
	docker-compose exec postgres psql -U telebilling -d order_management
