#!/usr/bin/env python3
"""Test script to verify TeleBilling setup."""

import asyncio
import json
import sys
from datetime import datetime
from uuid import uuid4

import httpx
import asyncpg
import redis
from kafka import KafkaProducer, KafkaConsumer


async def test_database():
    """Test PostgreSQL connection."""
    try:
        conn = await asyncpg.connect(
            "postgresql://telebilling:telebilling_dev_password@localhost:5432/order_management"
        )
        
        # Test basic query
        result = await conn.fetchval("SELECT 1")
        assert result == 1
        
        await conn.close()
        print("✅ PostgreSQL connection successful")
        return True
    except Exception as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        return False


def test_redis():
    """Test Redis connection."""
    try:
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        
        # Test basic operations
        r.set('test_key', 'test_value')
        value = r.get('test_key')
        assert value.decode() == 'test_value'
        r.delete('test_key')
        
        print("✅ Redis connection successful")
        return True
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        return False


def test_kafka():
    """Test Kafka connection."""
    try:
        # Test producer
        producer = KafkaProducer(
            bootstrap_servers=['localhost:9092'],
            value_serializer=lambda x: json.dumps(x).encode('utf-8')
        )
        
        test_message = {
            'test': True,
            'timestamp': datetime.utcnow().isoformat(),
            'message_id': str(uuid4())
        }
        
        producer.send('test_topic', test_message)
        producer.flush()
        producer.close()
        
        print("✅ Kafka connection successful")
        return True
    except Exception as e:
        print(f"❌ Kafka connection failed: {e}")
        return False


async def test_order_service():
    """Test Order Management Service."""
    try:
        async with httpx.AsyncClient() as client:
            # Test health endpoint
            response = await client.get("http://localhost:8001/health/")
            assert response.status_code == 200
            
            health_data = response.json()
            assert health_data["service"] == "order-management"
            
            # Test root endpoint
            response = await client.get("http://localhost:8001/")
            assert response.status_code == 200
            
            root_data = response.json()
            assert root_data["success"] is True
            
            print("✅ Order Management Service is running")
            return True
    except Exception as e:
        print(f"❌ Order Management Service test failed: {e}")
        return False


async def test_monitoring_services():
    """Test monitoring services."""
    services = {
        "Prometheus": "http://localhost:9090/-/healthy",
        "Grafana": "http://localhost:3000/api/health",
        "Elasticsearch": "http://localhost:9200/_cluster/health",
        "Kibana": "http://localhost:5601/api/status",
        "Jaeger": "http://localhost:16686/",
    }
    
    results = {}
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        for service, url in services.items():
            try:
                response = await client.get(url)
                if response.status_code in [200, 201]:
                    print(f"✅ {service} is running")
                    results[service] = True
                else:
                    print(f"⚠️  {service} returned status {response.status_code}")
                    results[service] = False
            except Exception as e:
                print(f"❌ {service} connection failed: {e}")
                results[service] = False
    
    return all(results.values())


async def create_test_order():
    """Create a test order to verify the API."""
    try:
        async with httpx.AsyncClient() as client:
            # Create test order
            order_data = {
                "customer_id": str(uuid4()),
                "order_type": "new_service",
                "priority": "normal",
                "contact_person": "Test User",
                "contact_email": "<EMAIL>",
                "contact_phone": "+1234567890",
                "notes": "Test order created by setup verification script",
                "tags": ["test", "setup_verification"]
            }
            
            response = await client.post(
                "http://localhost:8001/api/v1/orders/",
                json=order_data
            )
            
            if response.status_code == 201:
                order_response = response.json()
                order_id = order_response["data"]["order"]["id"]
                print(f"✅ Test order created successfully: {order_id}")
                
                # Test getting the order
                get_response = await client.get(f"http://localhost:8001/api/v1/orders/{order_id}")
                if get_response.status_code == 200:
                    print("✅ Test order retrieved successfully")
                    return True
                else:
                    print(f"❌ Failed to retrieve test order: {get_response.status_code}")
                    return False
            else:
                print(f"❌ Failed to create test order: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ Test order creation failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🧪 TeleBilling Setup Verification")
    print("=" * 40)
    
    tests = [
        ("Database (PostgreSQL)", test_database()),
        ("Cache (Redis)", test_redis()),
        ("Message Queue (Kafka)", test_kafka()),
        ("Order Management Service", test_order_service()),
        ("Monitoring Services", test_monitoring_services()),
        ("API Functionality", create_test_order()),
    ]
    
    results = []
    
    for test_name, test_coro in tests:
        print(f"\n🔍 Testing {test_name}...")
        if asyncio.iscoroutine(test_coro):
            result = await test_coro
        else:
            result = test_coro
        results.append((test_name, result))
    
    print("\n" + "=" * 40)
    print("📊 Test Results Summary")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your TeleBilling setup is working correctly.")
        print("\n🚀 You can now start developing:")
        print("   • Order Management API: http://localhost:8001/docs")
        print("   • Prometheus: http://localhost:9090")
        print("   • Grafana: http://localhost:3000 (admin/admin)")
        print("   • Kibana: http://localhost:5601")
        print("   • Jaeger: http://localhost:16686")
        return 0
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please check the setup.")
        print("\n🔧 Troubleshooting tips:")
        print("   • Make sure all Docker services are running: docker-compose ps")
        print("   • Check service logs: docker-compose logs <service-name>")
        print("   • Restart services: make restart")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
