"""SQLAlchemy models for Order Management Service."""

from datetime import datetime
from decimal import Decimal
from typing import List, Optional
from uuid import uuid4

from sqlalchemy import (
    Column, String, DateTime, Enum, Text, Integer, 
    Numeric, ForeignKey, JSON, Boolean
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.sql import func

from app.core.database import Base
from shared.models.order import OrderStatus, OrderType, OrderPriority


class OrderModel(Base):
    """SQLAlchemy model for orders."""
    
    __tablename__ = "orders"
    
    # Primary key
    id: Mapped[UUID] = mapped_column(
        UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid4
    )
    
    # Order identification
    order_number: Mapped[str] = mapped_column(String(50), unique=True, nullable=False)
    customer_id: Mapped[UUID] = mapped_column(UUID(as_uuid=True), nullable=False)
    
    # Order details
    order_type: Mapped[OrderType] = mapped_column(
        Enum(OrderType), 
        nullable=False
    )
    status: Mapped[OrderStatus] = mapped_column(
        Enum(OrderStatus), 
        nullable=False, 
        default=OrderStatus.DRAFT
    )
    priority: Mapped[OrderPriority] = mapped_column(
        Enum(OrderPriority), 
        nullable=False, 
        default=OrderPriority.NORMAL
    )
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now()
    )
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), 
        onupdate=func.now()
    )
    order_date: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        nullable=False,
        default=func.now()
    )
    
    # Date tracking
    requested_completion_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True)
    )
    scheduled_completion_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True)
    )
    actual_completion_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True)
    )
    
    # Financial information
    subtotal: Mapped[Decimal] = mapped_column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal("0.00")
    )
    tax_amount: Mapped[Decimal] = mapped_column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal("0.00")
    )
    total_amount: Mapped[Decimal] = mapped_column(
        Numeric(10, 2), 
        nullable=False, 
        default=Decimal("0.00")
    )
    
    # Contact information
    contact_person: Mapped[Optional[str]] = mapped_column(String(200))
    contact_phone: Mapped[Optional[str]] = mapped_column(String(20))
    contact_email: Mapped[Optional[str]] = mapped_column(String(255))
    service_address: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Workflow and assignment
    workflow_id: Mapped[Optional[UUID]] = mapped_column(UUID(as_uuid=True))
    assigned_technician: Mapped[Optional[str]] = mapped_column(String(200))
    
    # Notes and references
    notes: Mapped[Optional[str]] = mapped_column(Text)
    internal_notes: Mapped[Optional[str]] = mapped_column(Text)
    external_reference: Mapped[Optional[str]] = mapped_column(String(100))
    
    # Metadata
    tags: Mapped[Optional[list]] = mapped_column(JSON)
    
    # Relationships
    items: Mapped[List["OrderItemModel"]] = relationship(
        "OrderItemModel", 
        back_populates="order",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Order {self.order_number} - {self.status}>"


class OrderItemModel(Base):
    """SQLAlchemy model for order items."""
    
    __tablename__ = "order_items"
    
    # Primary key
    id: Mapped[UUID] = mapped_column(
        UUID(as_uuid=True), 
        primary_key=True, 
        default=uuid4
    )
    
    # Foreign keys
    order_id: Mapped[UUID] = mapped_column(
        UUID(as_uuid=True), 
        ForeignKey("orders.id"), 
        nullable=False
    )
    service_id: Mapped[UUID] = mapped_column(UUID(as_uuid=True), nullable=False)
    service_plan_id: Mapped[Optional[UUID]] = mapped_column(UUID(as_uuid=True))
    
    # Item details
    item_type: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[str] = mapped_column(String(500), nullable=False)
    quantity: Mapped[int] = mapped_column(Integer, nullable=False, default=1)
    
    # Pricing
    unit_price: Mapped[Decimal] = mapped_column(Numeric(10, 2), nullable=False)
    total_price: Mapped[Decimal] = mapped_column(Numeric(10, 2), nullable=False)
    
    # Configuration
    configuration: Mapped[Optional[dict]] = mapped_column(JSON)
    
    # Status tracking
    status: Mapped[OrderStatus] = mapped_column(
        Enum(OrderStatus), 
        nullable=False, 
        default=OrderStatus.DRAFT
    )
    provisioning_status: Mapped[Optional[str]] = mapped_column(String(100))
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), 
        server_default=func.now()
    )
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), 
        onupdate=func.now()
    )
    
    # Scheduling
    requested_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True)
    )
    scheduled_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True)
    )
    completed_date: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True)
    )
    
    # Notes and references
    notes: Mapped[Optional[str]] = mapped_column(Text)
    external_reference: Mapped[Optional[str]] = mapped_column(String(100))
    
    # Relationships
    order: Mapped["OrderModel"] = relationship(
        "OrderModel", 
        back_populates="items"
    )
    
    def __repr__(self) -> str:
        return f"<OrderItem {self.id} - {self.item_type}>"
