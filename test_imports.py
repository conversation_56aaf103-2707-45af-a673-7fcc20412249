#!/usr/bin/env python3
"""Simple test script to verify imports work correctly."""

import sys
import os

# Add the services directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'services', 'order-management'))
sys.path.insert(0, os.path.dirname(__file__))

def test_shared_models():
    """Test shared model imports."""
    try:
        from shared.models.base import APIResponse, HealthCheck, PaginationParams
        from shared.models.customer import Customer, CustomerCreate, CustomerUpdate
        from shared.models.order import Order, OrderCreate, OrderUpdate, OrderStatus
        print("✅ Shared models imported successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import shared models: {e}")
        return False

def test_order_service_config():
    """Test order service configuration."""
    try:
        from app.core.config import Settings
        settings = Settings()
        print("✅ Order service configuration loaded successfully")
        print(f"   - Service version: {settings.VERSION}")
        print(f"   - Debug mode: {settings.DEBUG}")
        print(f"   - Log level: {settings.LOG_LEVEL}")
        return True
    except Exception as e:
        print(f"❌ Failed to load order service config: {e}")
        return False

def test_order_models():
    """Test order service models."""
    try:
        from app.models.order import OrderModel, OrderItemModel
        print("✅ Order service models imported successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import order service models: {e}")
        return False

def test_order_service():
    """Test order service business logic."""
    try:
        # This will fail without a database, but we can test the import
        from app.services.order_service import OrderService
        print("✅ Order service business logic imported successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import order service: {e}")
        return False

def test_api_endpoints():
    """Test API endpoint imports."""
    try:
        from app.api.v1 import health, orders
        print("✅ API endpoints imported successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import API endpoints: {e}")
        return False

def test_pydantic_models():
    """Test creating Pydantic model instances."""
    try:
        from shared.models.base import APIResponse, HealthCheck
        from shared.models.order import OrderCreate, OrderType, OrderPriority
        from uuid import uuid4
        
        # Test APIResponse
        response = APIResponse.success_response(
            data={"test": "data"},
            message="Test successful"
        )
        assert response.success is True
        
        # Test HealthCheck
        health = HealthCheck.healthy("test-service", {"db": "healthy"})
        assert health.status == "healthy"
        
        # Test OrderCreate
        order_data = OrderCreate(
            customer_id=uuid4(),
            order_type=OrderType.NEW_SERVICE,
            priority=OrderPriority.NORMAL,
            contact_person="Test User",
            contact_email="<EMAIL>"
        )
        assert order_data.order_type == OrderType.NEW_SERVICE
        
        print("✅ Pydantic models work correctly")
        return True
    except Exception as e:
        print(f"❌ Failed to create Pydantic models: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing TeleBilling Order Management Service Imports")
    print("=" * 60)
    
    tests = [
        ("Shared Models", test_shared_models),
        ("Order Service Config", test_order_service_config),
        ("Order Models", test_order_models),
        ("Order Service Logic", test_order_service),
        ("API Endpoints", test_api_endpoints),
        ("Pydantic Model Creation", test_pydantic_models),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Testing {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All import tests passed! The Order Management Service structure is correct.")
        print("\n📝 Next steps:")
        print("   1. Install dependencies: pip install -r requirements.txt")
        print("   2. Start infrastructure: docker-compose up -d")
        print("   3. Run the service: python -m uvicorn app.main:app --reload --port 8001")
        print("   4. Test the API: http://localhost:8001/docs")
        return 0
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please check the code structure.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
