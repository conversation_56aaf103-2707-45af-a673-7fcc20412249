"""Health check endpoints for Order Management Service."""

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
import structlog

from app.core.database import get_db
from shared.models.base import HealthCheck

router = APIRouter()
logger = structlog.get_logger(__name__)


@router.get("/", response_model=HealthCheck)
async def health_check(db: AsyncSession = Depends(get_db)) -> HealthCheck:
    """Basic health check endpoint."""
    dependencies = {}
    
    # Check database connection
    try:
        result = await db.execute(text("SELECT 1"))
        await result.fetchone()
        dependencies["database"] = "healthy"
    except Exception as e:
        logger.error("Database health check failed", exc_info=e)
        dependencies["database"] = "unhealthy"
    
    # Check Redis connection
    # TODO: Add Redis health check
    dependencies["redis"] = "not_implemented"
    
    # Check Kafka connection
    # TODO: Add Kafka health check
    dependencies["kafka"] = "not_implemented"
    
    # Determine overall health
    is_healthy = all(status == "healthy" for status in dependencies.values() if status != "not_implemented")
    
    if is_healthy:
        return HealthCheck.healthy("order-management", dependencies)
    else:
        return HealthCheck.unhealthy("order-management", dependencies)


@router.get("/ready", response_model=HealthCheck)
async def readiness_check(db: AsyncSession = Depends(get_db)) -> HealthCheck:
    """Readiness check for Kubernetes."""
    # More strict checks for readiness
    try:
        # Check if we can perform a simple database operation
        result = await db.execute(text("SELECT COUNT(*) FROM orders LIMIT 1"))
        await result.fetchone()
        
        return HealthCheck.healthy("order-management", {"database": "ready"})
    except Exception as e:
        logger.error("Readiness check failed", exc_info=e)
        return HealthCheck.unhealthy("order-management", {"database": "not_ready"})


@router.get("/live", response_model=HealthCheck)
async def liveness_check() -> HealthCheck:
    """Liveness check for Kubernetes."""
    # Simple check that the service is running
    return HealthCheck.healthy("order-management", {"status": "alive"})
