#!/bin/bash

# TeleBilling Development Startup Script

set -e

echo "🚀 Starting TeleBilling Development Environment"

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if Poetry is installed
if ! command -v poetry &> /dev/null; then
    echo "❌ Poetry is not installed. Please install Poetry first."
    echo "   Visit: https://python-poetry.org/docs/#installation"
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. Please review and update as needed."
fi

# Install Python dependencies
echo "📦 Installing Python dependencies..."
poetry install

# Start infrastructure services
echo "🐳 Starting infrastructure services..."
docker-compose up -d postgres redis kafka zookeeper prometheus grafana elasticsearch kibana jaeger

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 30

# Check if PostgreSQL is ready
echo "🔍 Checking PostgreSQL connection..."
until docker-compose exec -T postgres pg_isready -U telebilling; do
    echo "Waiting for PostgreSQL..."
    sleep 2
done

# Check if Redis is ready
echo "🔍 Checking Redis connection..."
until docker-compose exec -T redis redis-cli ping; do
    echo "Waiting for Redis..."
    sleep 2
done

echo "✅ Infrastructure services are ready!"

# Run database migrations (when we add Alembic)
# echo "🗄️  Running database migrations..."
# poetry run alembic upgrade head

echo ""
echo "🎉 Development environment is ready!"
echo ""
echo "📋 Available services:"
echo "   • PostgreSQL:     localhost:5432"
echo "   • Redis:          localhost:6379"
echo "   • Kafka:          localhost:9092"
echo "   • Prometheus:     http://localhost:9090"
echo "   • Grafana:        http://localhost:3000 (admin/admin)"
echo "   • Elasticsearch:  http://localhost:9200"
echo "   • Kibana:         http://localhost:5601"
echo "   • Jaeger:         http://localhost:16686"
echo ""
echo "🚀 To start the Order Management Service:"
echo "   cd services/order-management"
echo "   poetry run uvicorn app.main:app --reload --port 8001"
echo ""
echo "📚 API Documentation will be available at:"
echo "   http://localhost:8001/docs"
echo ""
echo "🛑 To stop all services:"
echo "   docker-compose down"
