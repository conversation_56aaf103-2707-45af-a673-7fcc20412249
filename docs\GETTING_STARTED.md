# Getting Started with TeleBilling

This guide will help you set up and run the TeleBilling system on your local development environment.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Docker & Docker Compose** (v20.10+)
- **Python** (3.11+)
- **Poetry** (Python dependency management)
- **Git**

### Installing Prerequisites

#### Docker
- **Windows/Mac**: Download Docker Desktop from [docker.com](https://www.docker.com/products/docker-desktop)
- **Linux**: Follow the installation guide for your distribution

#### Poetry
```bash
curl -sSL https://install.python-poetry.org | python3 -
```

## Quick Start

### 1. <PERSON>lone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd TeleBilling

# Setup development environment
make setup
```

### 2. Start Infrastructure Services

```bash
# Start all infrastructure services (PostgreSQL, Redis, Kafka, etc.)
make start
```

This will start:
- PostgreSQL (port 5432)
- <PERSON><PERSON> (port 6379)
- <PERSON> (port 9092)
- Prometheus (port 9090)
- <PERSON><PERSON> (port 3000)
- Elasticsearch (port 9200)
- <PERSON><PERSON> (port 5601)
- <PERSON><PERSON><PERSON> (port 16686)

### 3. Start the Order Management Service

```bash
# In a new terminal
make start-order
```

The service will be available at:
- **API**: http://localhost:8001
- **Documentation**: http://localhost:8001/docs
- **Health Check**: http://localhost:8001/health

### 4. Verify Setup

```bash
# Run the verification script
python scripts/development/test-setup.py
```

## Development Workflow

### Available Commands

```bash
# Development
make start          # Start infrastructure services
make stop           # Stop all services
make restart        # Restart all services
make clean          # Clean up containers and volumes

# Services
make start-order    # Start Order Management Service
make start-inventory # Start Inventory Management Service (when implemented)
make start-billing  # Start Billing Integration Service (when implemented)

# Testing
make test           # Run all tests
make test-order     # Run Order Management tests
make test-coverage  # Run tests with coverage

# Code Quality
make lint           # Run linting
make format         # Format code
make type-check     # Run type checking
make security-check # Run security checks

# Database
make db-migrate     # Run database migrations
make db-reset       # Reset database
```

### Project Structure

```
TeleBilling/
├── services/                    # Microservices
│   ├── order-management/       # Order processing service
│   ├── inventory-management/   # Resource inventory service (planned)
│   ├── billing-integration/    # Billing service (planned)
│   └── orchestration-engine/   # Workflow orchestration (planned)
├── shared/                     # Shared libraries and models
│   ├── models/                 # Common data models
│   ├── events/                 # Event schemas (planned)
│   └── utils/                  # Utility functions (planned)
├── infrastructure/             # Infrastructure as Code (planned)
├── docs/                       # Documentation
├── scripts/                    # Development scripts
├── monitoring/                 # Monitoring configurations
└── tests/                      # Integration tests (planned)
```

## API Usage Examples

### Creating an Order

```bash
curl -X POST "http://localhost:8001/api/v1/orders/" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": "123e4567-e89b-12d3-a456-426614174000",
    "order_type": "new_service",
    "priority": "normal",
    "contact_person": "John Doe",
    "contact_email": "<EMAIL>",
    "contact_phone": "+1234567890",
    "notes": "New fiber internet service"
  }'
```

### Listing Orders

```bash
curl "http://localhost:8001/api/v1/orders/?page=1&size=10"
```

### Getting an Order

```bash
curl "http://localhost:8001/api/v1/orders/{order_id}"
```

## Monitoring and Observability

### Prometheus Metrics
- **URL**: http://localhost:9090
- **Metrics**: HTTP requests, response times, error rates
- **Custom Metrics**: Order processing metrics, business KPIs

### Grafana Dashboards
- **URL**: http://localhost:3000
- **Credentials**: admin/admin
- **Dashboards**: Service health, performance metrics, business metrics

### Distributed Tracing
- **URL**: http://localhost:16686
- **Features**: Request tracing across services, performance analysis

### Centralized Logging
- **Elasticsearch**: http://localhost:9200
- **Kibana**: http://localhost:5601
- **Log Format**: Structured JSON logs with correlation IDs

## Development Best Practices

### Code Style
- Follow PEP 8 guidelines
- Use type hints for all functions
- Write docstrings for public APIs
- Maintain >90% test coverage

### API Design
- RESTful endpoints with proper HTTP methods
- Consistent error handling and response formats
- OpenAPI 3.0 specification compliance
- API versioning (v1, v2, etc.)

### Database
- Use migrations for schema changes
- Follow naming conventions
- Index frequently queried columns
- Use transactions for data consistency

### Testing
- Unit tests for business logic
- Integration tests for API endpoints
- Contract tests for service boundaries
- Load tests for performance validation

## Troubleshooting

### Common Issues

#### Services Not Starting
```bash
# Check Docker status
docker info

# Check service logs
docker-compose logs <service-name>

# Restart services
make restart
```

#### Database Connection Issues
```bash
# Check PostgreSQL status
docker-compose exec postgres pg_isready -U telebilling

# Reset database
make db-reset
```

#### Port Conflicts
If you have port conflicts, update the ports in `docker-compose.yml`:
```yaml
services:
  postgres:
    ports:
      - "5433:5432"  # Change from 5432 to 5433
```

### Getting Help

1. **Check the logs**: `docker-compose logs <service-name>`
2. **Verify setup**: `python scripts/development/test-setup.py`
3. **Reset environment**: `make clean && make setup && make start`
4. **Check documentation**: Browse the `/docs` directory
5. **API documentation**: Visit http://localhost:8001/docs

## Next Steps

1. **Explore the API**: Use the interactive documentation at http://localhost:8001/docs
2. **Add more services**: Implement Inventory Management and Billing Integration services
3. **Customize configuration**: Update `.env` file for your environment
4. **Set up CI/CD**: Configure automated testing and deployment
5. **Add monitoring**: Create custom Grafana dashboards for your metrics

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with tests
4. Run the test suite: `make test`
5. Submit a pull request

For more detailed information, see the individual service documentation in their respective directories.
